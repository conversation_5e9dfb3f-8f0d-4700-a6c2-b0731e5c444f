import pandas as pd
import csv

from prepare_data import prepare_data


# 主执行流程
if __name__ == "__main__":
    
    # import csv
    # with open('data1.csv') as f:
    #     data = list(csv.reader(f))
    # print(data)
    
    # 读取数据
    raw_data = pd.read_csv('data1.csv', encoding='utf-8')
    print(raw_data)
    
    # 预处理
    clean_data = prepare_data(raw_data)
    print(clean_data)
    
    # # 综合预测
    # predictions = integrated_resource_prediction(clean_data)
    
    # # 生成报告
    # print("===== 应用资源预测报告 =====")
    # print(predictions)
    
    # # 可选：保存到文件
    # predictions.to_csv('resource_recommendations.csv', index=False)
    
    # print("\n===== 详细分析完成 =====")