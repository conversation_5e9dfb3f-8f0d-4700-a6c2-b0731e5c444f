def prepare_data(raw_data):
    """数据预处理函数"""
    
    row_data = raw_data.plot('\n')
    print(row_data)
    cleaned = raw_data.dropna()
    # 数据清洗
    cleaned = raw_data.dropna(subset=['pv', '主机数量', 'CPU使用率'])
    
    # 机器数量至少为1
    cleaned['主机数量'] = cleaned['主机数量'].clip(lower=1)
    
    # 处理使用率>100%的情况
    cleaned['CPU使用率'] = cleaned['CPU使用率'].clip(upper=100)
    cleaned['内存利用率'] = cleaned['内存利用率'].clip(upper=100)
    
    # 添加月份排序索引
    month_order = {month: idx for idx, month in enumerate(sorted(cleaned['月份'].unique()))}
    cleaned['month_index'] = cleaned['月份'].map(month_order)
    
    return cleaned.sort_values(['应用ID', 'month_index'])